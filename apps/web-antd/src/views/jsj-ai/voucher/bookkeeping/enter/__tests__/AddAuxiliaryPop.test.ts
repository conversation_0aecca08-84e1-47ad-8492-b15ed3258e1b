import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { message } from 'ant-design-vue';
import AddAuxiliaryPop from '../AddAuxiliaryPop.vue';

// Mock ant-design-vue message
vi.mock('ant-design-vue', () => ({
  message: {
    warning: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock emitter
vi.mock('../usermitt', () => ({
  default: {
    emit: vi.fn(),
  },
}));

describe('AddAuxiliaryPop 辅助核算验证', () => {
  let wrapper: any;

  beforeEach(() => {
    vi.clearAllMocks();
    wrapper = mount(AddAuxiliaryPop, {
      props: {
        auxiliaryType: 'customer',
      },
    });
  });

  it('应该阻止未开启辅助核算的科目新增辅助项目', async () => {
    const subjectInfo = {
      code: '1001',
      name: '库存现金',
      fullName: '1001 库存现金',
      useAssistant: false, // 未开启辅助核算
      assistantType: null,
    };

    // 调用 open 方法
    wrapper.vm.open(subjectInfo, 'customer');

    // 验证警告消息
    expect(message.warning).toHaveBeenCalledWith(
      '科目"1001 库存现金"未开启辅助核算功能，无法新增辅助项目。请在会计科目设置中开启该科目的辅助核算功能。'
    );

    // 验证弹框没有打开
    expect(wrapper.vm.visible).toBe(false);
  });

  it('应该阻止未配置辅助核算类型的科目新增辅助项目', async () => {
    const subjectInfo = {
      code: '1122',
      name: '应收账款',
      fullName: '1122 应收账款',
      useAssistant: true, // 开启了辅助核算
      assistantType: null, // 但未配置类型
    };

    // 调用 open 方法
    wrapper.vm.open(subjectInfo);

    // 验证警告消息
    expect(message.warning).toHaveBeenCalledWith(
      '科目"1122 应收账款"未配置辅助核算类型，无法新增辅助项目。请在会计科目设置中配置该科目的辅助核算类型。'
    );

    // 验证弹框没有打开
    expect(wrapper.vm.visible).toBe(false);
  });

  it('应该阻止辅助核算类型不匹配的情况', async () => {
    const subjectInfo = {
      code: '1122',
      name: '应收账款',
      fullName: '1122 应收账款',
      useAssistant: true,
      assistantType: 'c', // 客户类型
    };

    // 尝试新增供应商类型的辅助项目
    wrapper.vm.open(subjectInfo, 'supplier');

    // 验证警告消息
    expect(message.warning).toHaveBeenCalledWith(
      '科目"1122 应收账款"的辅助核算类型为"客户"，与要新增的"供应商"类型不匹配。请选择正确的辅助核算类型。'
    );

    // 验证弹框没有打开
    expect(wrapper.vm.visible).toBe(false);
  });

  it('应该允许正确配置的科目新增辅助项目', async () => {
    const subjectInfo = {
      code: '1122',
      name: '应收账款',
      fullName: '1122 应收账款',
      useAssistant: true,
      assistantType: 'c', // 客户类型
    };

    // 新增匹配的客户类型辅助项目
    wrapper.vm.open(subjectInfo, 'customer');

    // 验证没有警告消息
    expect(message.warning).not.toHaveBeenCalled();

    // 验证弹框打开了
    expect(wrapper.vm.visible).toBe(true);

    // 验证辅助类型设置正确
    expect(wrapper.vm.formState.type).toBe('customer');
  });

  it('应该阻止没有科目信息的情况', async () => {
    // 调用 open 方法但不传递科目信息
    wrapper.vm.open(null);

    // 验证警告消息
    expect(message.warning).toHaveBeenCalledWith(
      '请先选择科目，然后再新增辅助项目。'
    );

    // 验证弹框没有打开
    expect(wrapper.vm.visible).toBe(false);
  });

  it('应该正确映射API辅助核算类型', async () => {
    const subjectInfo = {
      code: '2202',
      name: '应付账款',
      fullName: '2202 应付账款',
      useAssistant: true,
      assistantType: 's', // API中的供应商类型
    };

    // 新增匹配的供应商类型辅助项目
    wrapper.vm.open(subjectInfo, 's');

    // 验证没有警告消息
    expect(message.warning).not.toHaveBeenCalled();

    // 验证弹框打开了
    expect(wrapper.vm.visible).toBe(true);

    // 验证辅助类型映射正确
    expect(wrapper.vm.formState.type).toBe('supplier');
  });

  it('应该允许新增科目选择任意辅助核算类型', async () => {
    const newSubjectInfo = {
      code: '6001',
      name: '主营业务成本',
      fullName: '6001 主营业务成本',
      isNewSubject: true, // 标记为新增科目
      p_account_code: '6000', // 上级科目代码
      useAssistant: undefined, // 新增科目可能没有这些字段
      assistantType: undefined,
    };

    // 尝试新增客户类型的辅助项目
    wrapper.vm.open(newSubjectInfo, 'customer');

    // 验证没有警告消息
    expect(message.warning).not.toHaveBeenCalled();

    // 验证弹框打开了
    expect(wrapper.vm.visible).toBe(true);

    // 验证辅助类型设置正确
    expect(wrapper.vm.formState.type).toBe('customer');

    // 验证类型选择器没有被禁用
    expect(wrapper.vm.isTypeDisabled).toBe(false);
  });

  it('应该允许新增科目通过p_account_code识别', async () => {
    const newSubjectInfo = {
      code: '1403',
      name: '预付账款',
      fullName: '1403 预付账款',
      p_account_code: '1400', // 通过p_account_code识别为新增科目
      // 没有isNewSubject标记
    };

    // 尝试新增供应商类型的辅助项目
    wrapper.vm.open(newSubjectInfo, 'supplier');

    // 验证没有警告消息
    expect(message.warning).not.toHaveBeenCalled();

    // 验证弹框打开了
    expect(wrapper.vm.visible).toBe(true);

    // 验证辅助类型设置正确
    expect(wrapper.vm.formState.type).toBe('supplier');

    // 验证类型选择器没有被禁用
    expect(wrapper.vm.isTypeDisabled).toBe(false);
  });
});
