// antd的message组件
// import { message } from 'ant-design-vue';

// 类型定义
export interface ApiResponse<T = any> {
  returnCode: string;
  returnMsg: string;
  data: T;
}

export interface SummaryItm {
  id: string;
  text: string;
}

export type SummaryItmData = SummaryItm[];

export interface LedgerItm {
  id: string;
  name: string;
  code: string;
  text: string;
  fullName: string;
}

export interface AuxiliaryItm {
  id: string;
  name: string;
}
export interface VoucherSaveDataItm {
  credit?: string;
  debit?: string;
  subjectId: string;
  summary: string;
}
 
export interface VoucherSaveData {
  attachmentCount: number;
  credit: string;
  dateTime: number;
  debit: string;
  detail: VoucherSaveDataItm[];
  insertMode: boolean;
  insertNumber: any;
  insertWord: any;
  voucherNo: number;
  voucherNumber: number;
  voucherType: 'NORMAL';
  voucherWord: string;
}

export interface VoucherTemplate {
  attachmentCount: number;
  detail: VoucherSaveDataItm[];
  id?: string;
  isAmount: boolean;
  name: string;
}
